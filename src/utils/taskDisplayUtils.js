// src/utils/taskDisplayUtils.js

export const getPriorityColor = priority => {
  switch (priority) {
    case 'High': return 'error';
    case 'Medium': return 'warning';
    case 'Low': return 'success';
    default: return 'grey';
  }
};

export const getStatusColor = status => {
  switch (status) {
    case 'Done': return 'success';
    case 'In Progress': return 'info';
    case 'Blocked': return 'error';
    case 'Backlog': return 'grey';
    default: return 'grey';
  }
};

export const getTypeIcon = type => {
  switch (type) {
    case 'Story': return 'mdi-book-open-page-variant';
    case 'Task': return 'mdi-checkbox-marked-circle';
    case 'Epic': return 'mdi-flag';
    case 'Bug': return 'mdi-bug';
    default: return 'mdi-file-document';
  }
};

// This function was in TaskFilters.vue, might be useful globally
export const getTypeColor = type => {
    switch (type) {
      case 'Story': return 'primary';
      case 'Task': return 'success';
      case 'Epic': return 'warning';
      case 'Bug': return 'error';
      default: return 'grey';
    }
};

export const formatDate = dateString => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleString();
};

export const truncateText = (text, maxLength) => {
  if (!text) return '';
  return text.length > maxLength ? text.slice(0, Math.max(0, maxLength)) + '...' : text;
};
