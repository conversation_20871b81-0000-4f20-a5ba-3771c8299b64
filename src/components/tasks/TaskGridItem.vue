<template>
  <v-card
    :border="getPriorityColor(task.priority)"
    class="h-100 cursor-pointer"
    elevation="1"
    hover
    @click="emit('viewTaskDetails', task.task_id)"
  >
    <v-card-title class="text-wrap">
      <div class="d-flex align-start justify-space-between">
        <span class="flex-1-1">{{ task.summary }}</span>
        <v-chip
          class="ml-2"
          :color="getPriorityColor(task.priority)"
          size="small"
        >
          {{ task.priority }}
        </v-chip>
      </div>
    </v-card-title>

    <v-card-text>
      <div class="mb-3">
        <div class="text-body-2 text-medium-emphasis mb-2">{{ task.task_id }}</div>
        <p v-if="task.description" class="text-body-2 text-medium-emphasis">
          {{ truncateText(task.description, 100) }}
        </p>
      </div>

      <div class="d-flex flex-wrap ga-1 mb-3">
        <v-chip size="x-small" variant="outlined">
          {{ task.type }}
        </v-chip>
        <v-chip
          :color="getStatusColor(task.status)"
          size="x-small"
          variant="tonal"
        >
          {{ task.status }}
        </v-chip>
        <v-chip v-if="task.epic" color="primary" size="x-small" variant="text">
          {{ task.epic }}
        </v-chip>
      </div>
    </v-card-text>

    <v-card-actions>
      <v-btn
        color="primary"
        variant="text"
        @click.stop="emit('viewTaskDetails', task.task_id)"
      >
        <v-icon start>mdi-eye</v-icon>
        View Details
      </v-btn>
      <v-spacer />
      <v-menu>
        <template #activator="{ props: menuProps }">
          <v-btn
            v-bind="menuProps"
            size="small"
            variant="outlined"
            @click.stop
          >
            Change Status
            <v-icon end>mdi-chevron-down</v-icon>
          </v-btn>
        </template>
        <v-list>
          <v-list-item @click="emit('updateStatus', task, 'Backlog')">
            <v-list-item-title>Backlog</v-list-item-title>
          </v-list-item>
          <v-list-item @click="emit('updateStatus', task, 'In Progress')">
            <v-list-item-title>In Progress</v-list-item-title>
          </v-list-item>
          <v-list-item @click="emit('updateStatus', task, 'Done')">
            <v-list-item-title>Done</v-list-item-title>
          </v-list-item>
          <v-list-item @click="emit('updateStatus', task, 'Blocked')">
            <v-list-item-title>Blocked</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-card-actions>
  </v-card>
</template>

<script setup>
import {
  getPriorityColor,
  getStatusColor,
  truncateText,
} from '@/utils/taskDisplayUtils';

defineProps({
  task: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['viewTaskDetails', 'updateStatus']);
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}
.text-wrap {
  word-break: break-word;
  white-space: normal;
}
.v-card {
  transition: all 0.3s ease;
}
.v-card:hover {
  transform: translateY(-2px);
}
.v-chip {
  font-weight: 500;
}
</style>
