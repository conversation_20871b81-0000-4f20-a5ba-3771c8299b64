/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppFooter: typeof import('./src/components/AppFooter.vue')['default']
    DashboardStats: typeof import('./src/components/dashboard/DashboardStats.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    ProjectCard: typeof import('./src/components/projects/ProjectCard.vue')['default']
    ProjectManager: typeof import('./src/components/projects/ProjectManager.vue')['default']
    ProjectSelector: typeof import('./src/components/projects/ProjectSelector.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TaskFilters: typeof import('./src/components/TaskFilters.vue')['default']
    TaskForm: typeof import('./src/components/tasks/TaskForm.vue')['default']
    TaskGridItem: typeof import('./src/components/tasks/TaskGridItem.vue')['default']
    TaskListItem: typeof import('./src/components/tasks/TaskListItem.vue')['default']
    TaskStatistics: typeof import('./src/components/TaskStatistics.vue')['default']
    TaskUploadInfo: typeof import('./src/components/TaskUploadInfo.vue')['default']
  }
}
